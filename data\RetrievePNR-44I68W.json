{"seriesNum": "299", "PNR": "44I68W", "bookAgent": "WEB_MOBILE", "resCurrency": "AED", "PNRPin": "83129856", "bookDate": "2025-05-26T05:34:04", "modifyDate": "2025-06-06T15:32:18", "resType": "MOBILE", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 1, "activeSegCount": 1, "webBookingID": "8ef20h06ab0cedf4zfy945yb1cta7a37ufec4f0a7631", "securityGUID": "8ef20h06ab0cedf4zfy945yb1cta7a37ufec4f0a7631", "lastLoadGUID": "b9eaf722-5ff2-49eb-9f29-185bfbccac2b", "isAsyncPNR": false, "MasterPNR": "44I68W", "segments": [{"segKey": "16066156:16066156:5/27/2025 9:35:00 AM", "LFID": 16066156, "depDate": "2025-05-27T00:00:00", "flightGroupId": "16066156", "org": "DXB", "dest": "DOH", "depTime": "2025-05-27T09:35:00", "depTimeGMT": "2025-05-27T05:35:00", "arrTime": "2025-05-27T09:55:00", "operCarrier": "FZ", "operFlightNum": "003", "mrktCarrier": "FZ ", "mrktFlightNum": "003", "persons": [{"recNum": 1, "status": 5}], "legDetails": [{"PFID": 180997, "depDate": "2025-05-27T09:35:00", "legKey": "16066156:180997:5/27/2025 9:35:00 AM", "customerKey": "97843D12A8B1FA7434C9CB3B83F91685831352BEAE2646EF15737F091C417071"}], "active": true}, {"segKey": "16066157:16066157:6/3/2025 10:00:00 AM", "LFID": 16066157, "depDate": "2025-06-03T00:00:00", "flightGroupId": "16066157", "org": "DOH", "dest": "DXB", "depTime": "2025-06-03T10:00:00", "depTimeGMT": "2025-06-03T07:00:00", "arrTime": "2025-06-03T12:15:00", "operCarrier": "FZ", "operFlightNum": "002", "mrktCarrier": "FZ ", "mrktFlightNum": "002", "persons": [{"recNum": 2, "status": 0}], "legDetails": [{"PFID": 180998, "depDate": "2025-06-03T10:00:00", "legKey": "16066157:180998:6/3/2025 10:00:00 AM", "customerKey": "B776841865E9F0EA4737B90D98294D8175309F6450125E633F2D0D821F79F72D"}], "active": true}, {"segKey": "16087271:16087271:6/11/2025 5:05:00 PM", "LFID": 16087271, "depDate": "2025-06-11T00:00:00", "flightGroupId": "16087271", "org": "DOH", "dest": "DXB", "depTime": "2025-06-11T17:05:00", "depTimeGMT": "2025-06-11T14:05:00", "arrTime": "2025-06-11T19:20:00", "operCarrier": "FZ", "operFlightNum": "018", "mrktCarrier": "FZ ", "mrktFlightNum": "018", "persons": [{"recNum": 4, "status": 1}], "legDetails": [{"PFID": 181022, "depDate": "2025-06-11T17:05:00", "legKey": "16087271:181022:6/11/2025 5:05:00 PM", "customerKey": "ECFEB3F6EC4A5A3B251749482B2C32707D74027AEE0A68D8E1BAC03FF5EFFD4C"}], "active": true, "changeType": "TK"}, {"segKey": "16087271:16087271:6/7/2025 5:05:00 PM", "LFID": 16087271, "depDate": "2025-06-07T00:00:00", "flightGroupId": "16087271", "org": "DOH", "dest": "DXB", "depTime": "2025-06-07T17:05:00", "depTimeGMT": "2025-06-07T14:05:00", "arrTime": "2025-06-07T19:20:00", "operCarrier": "FZ", "operFlightNum": "018", "mrktCarrier": "FZ ", "mrktFlightNum": "018", "persons": [{"recNum": 3, "status": 0}], "legDetails": [{"PFID": 181022, "depDate": "2025-06-07T17:05:00", "legKey": "16087271:181022:6/7/2025 5:05:00 PM", "customerKey": "8EB0B71F65DDF6075D5FA89543172421992416DF01BF411B288893B9F0B846B6"}], "active": true, "changeType": "TK"}], "persons": [{"paxID": 270151141, "fName": "HANAN", "lName": "FRAIWAT", "title": "MS", "PTCID": 1, "gender": "F", "FFNum": "495417952", "FFTier": "SILVER", "TierID": "4", "recNum": [1, 2, 3, 4], "nameChangeCount": "1"}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB_MOBILE", "statusReasonID": 0, "markFareClass": "R", "insuPurchasedate": "5/26/2025 5:34:04 AM", "provider": "<PERSON>", "status": 5, "fareClass": "R", "operFareClass": "R", "FBC": "RRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuConfNum": "AYMBP-LSTJA-INS", "insuTransID": "AYMBP-LSTJA-INS/0899ca05-1b23-45ce-95c2-61ba84fa9b9f", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6833fc09000777000001f32b#1#1#MOBILE#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 12, "bookDate": "2025-05-26T05:34:04"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB_MOBILE", "cancelAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "R", "insuPurchasedate": "5/26/2025 5:34:04 AM", "provider": "<PERSON>", "status": 0, "fareClass": "R", "operFareClass": "R", "FBC": "RRL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "insuConfNum": "AYMBP-LSTJA-INS", "insuTransID": "AYMBP-LSTJA-INS/0899ca05-1b23-45ce-95c2-61ba84fa9b9f", "toRecNum": 3, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6833fc09000777000001f32b#1#2#MOBILE#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 12, "cancelReasonID": 0, "bookDate": "2025-05-26T05:34:04"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "WEB2_LIVE", "cancelAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "N", "insuPurchasedate": "5/26/2025 5:34:04 AM", "provider": "<PERSON>", "status": 0, "fareClass": "N", "operFareClass": "N", "FBC": "NRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuConfNum": "AYMBP-LSTJA-INS", "insuTransID": "AYMBP-LSTJA-INS/0899ca05-1b23-45ce-95c2-61ba84fa9b9f", "toRecNum": 4, "fromRecNum": 2, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "683dcd070007770000031667#270151141#2#WEB#VAYANT#CHANGE", "fareTypeID": 12, "channelID": 2, "cancelReasonID": 0, "bookDate": "2025-06-02T16:15:13"}]}, {"recNum": 4, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "N", "insuPurchasedate": "6/6/2025 3:32:11 PM", "provider": "<PERSON>", "status": 1, "fareClass": "N", "operFareClass": "N", "FBC": "NRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "7EVRP-BESEZ-INS/25d397e4-35e8-495a-8439-7bf40d1d2a8f", "fromRecNum": 3, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "684308d300077800000273a4#270151141#2#WEB#VAYANT#CHANGE", "fareTypeID": 12, "channelID": 2, "bookDate": "2025-06-06T15:32:11"}]}], "payments": [{"paymentID": *********, "paxID": 270151242, "method": "VISA", "status": "1", "paidDate": "2025-05-26T05:34:54", "cardNum": "************2933", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 2047.79, "baseCurr": "AED", "baseAmt": 2047.79, "userID": "WEB_MOBILE", "channelID": 12, "cardHolderName": "<PERSON><PERSON>", "authCode": "020824", "reference": "23253750", "externalReference": "23253750", "tranId": "21629321", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21629321}, {"paymentID": *********, "paxID": 271408687, "method": "VISA", "status": "1", "paidDate": "2025-06-06T15:32:15", "cardNum": "************0637", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 950.82, "baseCurr": "AED", "baseAmt": 950.82, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON><PERSON>", "authCode": "462233", "reference": "23492018", "externalReference": "23492018", "tranId": "21869341", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21869341}, {"paymentID": *********, "paxID": 270996158, "method": "VISA", "status": "1", "paidDate": "2025-06-02T16:15:19", "cardNum": "************0637", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 20.6, "baseCurr": "AED", "baseAmt": 20.6, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON><PERSON>", "authCode": "663767", "reference": "23408774", "externalReference": "23408774", "tranId": "21790854", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21790854}], "contacts": [{"key": 330201898, "paxID": 270151141, "contactField": "<EMAIL>", "contactType": "Email", "PreferenceOrder": "1", "countryCode": "", "areaCode": "", "phoneNumber": ""}, {"key": 330201897, "paxID": 270151141, "contactField": "971506214930", "contactType": "MobilePhone", "preferred": true, "PreferenceOrder": "1", "countryCode": "971", "areaCode": "", "phoneNumber": "506214930"}, {"key": 330201896, "paxID": 270151141, "contactField": "971506214930", "contactType": "WhatsApp", "PreferenceId": 13158620, "PreferenceOrder": "1", "countryCode": "971", "areaCode": "", "phoneNumber": "506214930"}], "OAFlights": null, "physicalFlights": [{"key": "16066156:180997:2025-05-27T09:35:00 AM", "LFID": 16066156, "PFID": 180997, "org": "DXB", "dest": "DOH", "depDate": "2025-05-27T09:35:00", "depTime": "2025-05-27T09:35:00", "arrTime": "2025-05-27T09:55:00", "carrier": "FZ", "flightNum": "003", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 1", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "003", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "DOH", "operatingCarrier": "FZ", "flightDuration": 4800, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Doha", "isActive": false}, {"key": "16066157:180998:2025-06-03T10:00:00 AM", "LFID": 16066157, "PFID": 180998, "org": "DOH", "dest": "DXB", "depDate": "2025-06-03T10:00:00", "depTime": "2025-06-03T10:00:00", "arrTime": "2025-06-03T12:15:00", "carrier": "FZ", "flightNum": "002", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "002", "flightStatus": "CLOSED", "originMetroGroup": "DOH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Doha", "destinationName": "Dubai International Airport", "isActive": false}, {"key": "16087271:181022:2025-06-07T05:05:00 PM", "LFID": 16087271, "PFID": 181022, "org": "DOH", "dest": "DXB", "depDate": "2025-06-07T17:05:00", "depTime": "2025-06-07T17:05:00", "arrTime": "2025-06-07T19:20:00", "carrier": "FZ", "flightNum": "018", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "018", "flightStatus": "CLOSED", "originMetroGroup": "DOH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Doha", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "5/23/2025 1:46:50 PM"}, {"key": "16087271:181022:2025-06-11T05:05:00 PM", "LFID": 16087271, "PFID": 181022, "org": "DOH", "dest": "DXB", "depDate": "2025-06-11T17:05:00", "depTime": "2025-06-11T17:05:00", "arrTime": "2025-06-11T19:20:00", "carrier": "FZ", "flightNum": "018", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "018", "flightStatus": "OPEN", "originMetroGroup": "DOH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Doha", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "TK", "flightChangeTime": "5/23/2025 1:47:01 PM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1361840108, "codeType": "INSU", "amt": 22.57, "curr": "AED", "originalAmt": 22.57, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-26T05:34:04", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1361840108:*********", "paymentID": *********, "amt": 22.57, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"12.29\",\r\n  \"Tax\": \"0.59\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-05-26T05:34:03"}, {"chargeID": 1378889351, "codeType": "INSU", "amt": -22.57, "curr": "AED", "originalAmt": -22.57, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-06T15:32:11", "desc": "INSU", "comment": "Reverse due to <PERSON>", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1361840108, "paymentMap": [{"key": "1378889351:*********", "paymentID": *********, "amt": -22.57, "approveCode": 0}]}, {"chargeID": 1361840091, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1361839968, "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-26T05:34:04", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1361840091:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1361840093, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1361839968, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-26T05:34:04", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1361840093:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1361840094, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1361839968, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-26T05:34:04", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1361840094:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1361840095, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1361839968, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-26T05:34:04", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1361840095:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1361840092, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1361839968, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-26T05:34:04", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1361840092:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1361840090, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1361839968, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-26T05:34:04", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1361840090:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1361839968, "codeType": "AIR", "amt": 678, "curr": "AED", "originalAmt": 678, "originalCurr": "AED", "status": 1, "billDate": "2025-05-26T05:34:04", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 150, "tierPoints": 150, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 40, "PromoTier": 0, "paymentMap": [{"key": "1361839968:*********", "paymentID": *********, "amt": 678, "approveCode": 0}], "bonusMiles": 40, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1361843610, "codeType": "PMNT", "amt": 59.64, "curr": "AED", "originalAmt": 59.64, "originalCurr": "AED", "status": 1, "billDate": "2025-05-26T05:34:58", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1361843610:*********", "paymentID": *********, "amt": 59.64, "approveCode": 0}]}, {"chargeID": 1361840089, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1361839968, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-26T05:34:04", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1361840112, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-26T05:34:04", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "180997", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 2, "charges": [{"chargeID": 1361840110, "codeType": "INSU", "amt": 22.58, "curr": "AED", "originalAmt": 22.58, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-26T05:34:04", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1361840110:*********", "paymentID": *********, "amt": 22.58, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"12.29\",\r\n  \"Tax\": \"0.59\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-05-26T05:34:03"}, {"chargeID": 1361840103, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1361840097, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-26T05:34:04", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1361840103:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1361840099, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1361840097, "amt": 70, "curr": "AED", "originalAmt": 70, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-26T05:34:04", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1361840099:*********", "paymentID": *********, "amt": 70, "approveCode": 0}]}, {"chargeID": 1361840104, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1361840097, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-26T05:34:04", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1361840104:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1361840101, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1361840097, "amt": 70, "curr": "AED", "originalAmt": 70, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-26T05:34:04", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1361840101:*********", "paymentID": *********, "amt": 70, "approveCode": 0}]}, {"chargeID": 1361840102, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1361840097, "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-26T05:34:04", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1361840102:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1361840098, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1361840097, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-26T05:34:04", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1361840098:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1373357092, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1361840097, "amt": -70, "curr": "AED", "originalAmt": -70, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-02T16:15:13", "desc": "Airport Fee.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1361840099, "paymentMap": [{"key": "1373357092:*********", "paymentID": *********, "amt": -70, "approveCode": 0}]}, {"chargeID": 1373357093, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1361840097, "amt": -70, "curr": "AED", "originalAmt": -70, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-02T16:15:13", "desc": "Passenger Facility Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1361840101, "paymentMap": [{"key": "1373357093:*********", "paymentID": *********, "amt": -70, "approveCode": 0}]}, {"chargeID": 1373357097, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1361840097, "amt": -80, "curr": "AED", "originalAmt": -80, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-02T16:15:13", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1361840102, "paymentMap": [{"key": "1373357097:*********", "paymentID": *********, "amt": -80, "approveCode": 0}]}, {"chargeID": 1373357096, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1361840097, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-02T16:15:13", "desc": "Passenger Service Charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1361840103, "paymentMap": [{"key": "1373357096:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1373357098, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1361840097, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-02T16:15:13", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1361840098, "paymentMap": [{"key": "1373357098:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1373357094, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1361840097, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-02T16:15:13", "desc": "Passenger safety and security fees (PSSF)", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1361840104, "paymentMap": [{"key": "1373357094:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1361840097, "codeType": "AIR", "amt": 800, "curr": "AED", "originalAmt": 800, "originalCurr": "AED", "status": 0, "billDate": "2025-05-26T05:34:04", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1361840097:*********", "paymentID": *********, "amt": 800, "approveCode": 0}]}, {"chargeID": 1373357095, "codeType": "AIR", "amt": -800, "curr": "AED", "originalAmt": -800, "originalCurr": "AED", "status": 0, "billDate": "2025-06-02T16:15:13", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1361840097, "paymentMap": [{"key": "1373357095:*********", "paymentID": *********, "amt": -800, "approveCode": 0}]}, {"chargeID": 1361840111, "codeType": "FRST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-26T05:34:04", "desc": "FRST", "comment": "FLXID:SPST_ZONE1_WIN_AIS::180998", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "180998"}, {"chargeID": 1361840105, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1361840097, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-26T05:34:04", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1361840100, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1361840097, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-26T05:34:04", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1361840113, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-26T05:34:04", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "180998"}]}, {"recNum": 4, "charges": [{"chargeID": 1378889331, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 1, "exchRate": 1, "billDate": "2025-06-06T15:32:11", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1378889331:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"SegPaxCount\":\"1\"}", "ChargeBookDate": "2025-06-06T15:32:11"}, {"chargeID": 1378889128, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1378889125, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-06T15:32:11", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1378889128:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1378889130, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1378889125, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-06T15:32:11", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1378889130:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1378889127, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1378889125, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-06T15:32:11", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1378889127:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1378889126, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1378889125, "amt": 70, "curr": "AED", "originalAmt": 70, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-06T15:32:11", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1378889126:*********", "paymentID": *********, "amt": 70, "approveCode": 0}]}, {"chargeID": 1378889131, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1378889125, "amt": 70, "curr": "AED", "originalAmt": 70, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-06T15:32:11", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1378889131:*********", "paymentID": *********, "amt": 70, "approveCode": 0}]}, {"chargeID": 1378889129, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1378889125, "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-06T15:32:11", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1378889129:*********", "paymentID": *********, "amt": 22.57, "approveCode": 0}, {"key": "1378889129:*********", "paymentID": *********, "amt": 57.43, "approveCode": 0}]}, {"chargeID": 1378889125, "codeType": "AIR", "amt": 830, "curr": "AED", "originalAmt": 830, "originalCurr": "AED", "status": 1, "billDate": "2025-06-06T15:32:11", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 2, "basePoints": 150, "tierPoints": 150, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 40, "PromoTier": 0, "paymentMap": [{"key": "1378889125:*********", "paymentID": *********, "amt": 830, "approveCode": 0}], "bonusMiles": 40, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1378892866, "codeType": "PMNT", "amt": 27.69, "curr": "AED", "originalAmt": 27.69, "originalCurr": "AED", "status": 1, "billDate": "2025-06-06T15:32:18", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1378892866:*********", "paymentID": *********, "amt": 27.69, "approveCode": 0}]}, {"chargeID": 1378889132, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1378889125, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-06T15:32:11", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1378889138, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-06T15:32:11", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181022", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 3, "charges": [{"chargeID": 1378889122, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1373357099, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-06T15:32:11", "desc": "Passenger Service Charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1373357104, "paymentMap": [{"key": "1378889122:*********", "paymentID": *********, "amt": -10, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-06-06T15:32:11"}, {"chargeID": 1378889117, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1373357099, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-06T15:32:11", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1373357102, "paymentMap": [{"key": "1378889117:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1378889119, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1373357099, "amt": -70, "curr": "AED", "originalAmt": -70, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-06T15:32:11", "desc": "Passenger Facility Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1373357105, "paymentMap": [{"key": "1378889119:*********", "paymentID": *********, "amt": -70, "approveCode": 0}]}, {"chargeID": 1378889118, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1373357099, "amt": -70, "curr": "AED", "originalAmt": -70, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-06T15:32:11", "desc": "Airport Fee.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1373357100, "paymentMap": [{"key": "1378889118:*********", "paymentID": *********, "amt": -70, "approveCode": 0}]}, {"chargeID": 1378889123, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1373357099, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-06T15:32:11", "desc": "Passenger safety and security fees (PSSF)", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1373357101, "paymentMap": [{"key": "1378889123:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1378889121, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1373357099, "amt": -80, "curr": "AED", "originalAmt": -80, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-06T15:32:11", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1373357103, "paymentMap": [{"key": "1378889121:*********", "paymentID": *********, "amt": -80, "approveCode": 0}]}, {"chargeID": 1373357102, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1373357099, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-02T16:15:13", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1373357102:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1373357100, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1373357099, "amt": 70, "curr": "AED", "originalAmt": 70, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-02T16:15:13", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1373357100:*********", "paymentID": *********, "amt": 70, "approveCode": 0}]}, {"chargeID": 1373357101, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1373357099, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-02T16:15:13", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1373357101:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1373357103, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1373357099, "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-02T16:15:13", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1373357103:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1373357104, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1373357099, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-02T16:15:13", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1373357104:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1373357105, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1373357099, "amt": 70, "curr": "AED", "originalAmt": 70, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-02T16:15:13", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1373357105:*********", "paymentID": *********, "amt": 70, "approveCode": 0}]}, {"chargeID": 1378889120, "codeType": "AIR", "amt": -820, "curr": "AED", "originalAmt": -820, "originalCurr": "AED", "status": 0, "billDate": "2025-06-06T15:32:11", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1373357099, "paymentMap": [{"key": "1378889120:*********", "paymentID": *********, "amt": -20, "approveCode": 0}, {"key": "1378889120:*********", "paymentID": *********, "amt": -800, "approveCode": 0}]}, {"chargeID": 1373357099, "codeType": "AIR", "amt": 820, "curr": "AED", "originalAmt": 820, "originalCurr": "AED", "status": 0, "billDate": "2025-06-02T16:15:13", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1373357099:*********", "paymentID": *********, "amt": 20, "approveCode": 0}, {"key": "1373357099:*********", "paymentID": *********, "amt": 800, "approveCode": 0}]}, {"chargeID": 1373362721, "codeType": "PMNT", "amt": 0.6, "curr": "AED", "originalAmt": 0.6, "originalCurr": "AED", "status": 0, "billDate": "2025-06-02T16:15:21", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1373362721:*********", "paymentID": *********, "amt": 0.6, "approveCode": 0}]}, {"chargeID": 1378889124, "codeType": "PNLT", "amt": 900, "curr": "AED", "originalAmt": 900, "originalCurr": "AED", "status": 1, "billDate": "2025-06-06T15:32:11", "desc": "Penalty AddedDueToModify FZ  018 DOH  - DXB  07-Jun-2025", "comment": "Penalty Added Due To Modify", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1378889124:*********", "paymentID": *********, "amt": 880, "approveCode": 0}, {"key": "1378889124:*********", "paymentID": *********, "amt": 20, "approveCode": 0}]}, {"chargeID": 1373357106, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1373357099, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-02T16:15:13", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1373357113, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-02T16:15:13", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181022"}]}], "parentPNRs": [], "childPNRs": []}