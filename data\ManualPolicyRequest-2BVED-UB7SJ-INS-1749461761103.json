{"pnr": "48QA00", "policyId": "2BVED-UB7SJ-INS", "timestamp": "2025-06-09T09:36:01.102Z", "requestDetails": {"url": "https://api.xcover.com/x/partners/ZDTIY/bookings/2BVED-UB7SJ-INS", "method": "POST", "headers": {"Content-Type": "application/json"}, "payload": {"quotes": [{"id": "2cbbfc1c-e198-4c5d-854e-bbba0345c66c", "insured": [{"first_name": "MOHAMMAD", "last_name": "BAHMAN"}]}], "policyholder": {"first_name": "MOHAMMAD", "last_name": "BAHMAN", "email": "<EMAIL>", "country": "KW"}}}, "curlCommand": "curl --location 'https://api.xcover.com/x/partners/ZDTIY/bookings/2BVED-UB7SJ-INS' \\\n--header 'Content-Type: application/json' \\\n--data-raw '{\n  \"quotes\": [\n    {\n      \"id\": \"2cbbfc1c-e198-4c5d-854e-bbba0345c66c\",\n      \"insured\": [\n        {\n          \"first_name\": \"MOHAMMAD\",\n          \"last_name\": \"BAHMAN\"\n        }\n      ]\n    }\n  ],\n  \"policyholder\": {\n    \"first_name\": \"MOHAMMAD\",\n    \"last_name\": \"BAHMAN\",\n    \"email\": \"<EMAIL>\",\n    \"country\": \"KW\"\n  }\n}'", "instructions": ["1. Verify the email address in the policyholder section is correct", "2. Verify passenger names are correct", "3. Confirm the country code is appropriate", "4. Execute the curl command manually", "5. Save the response for record keeping"], "notes": {"extractedPassengers": 1, "extractedEmail": "<EMAIL>", "primaryPassengerId": 270600453, "firstSegmentOrigin": "KWI", "detectedCountry": "KW", "quoteId": "2cbbfc1c-e198-4c5d-854e-bbba0345c66c", "quoteIdSource": "insuTransID", "hasContactInfo": true, "emailFromPrimaryPax": true}}