{"seriesNum": "299", "PNR": "48QA00", "bookAgent": "MOBILE_APP", "resCurrency": "AED", "PNRPin": "83247938", "bookDate": "2025-05-29T17:10:41", "modifyDate": "2025-06-06T12:43:38", "resType": "MOBILE", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 1, "activeSegCount": 1, "webBookingID": "3e41314281cek3f4nl44f40btae4iaub35de022fb5f0", "securityGUID": "3e41314281cek3f4nl44f40btae4iaub35de022fb5f0", "lastLoadGUID": "91c44d8e-65f7-478e-b3fa-cb248f7204be", "isAsyncPNR": false, "MasterPNR": "48QA00", "segments": [{"segKey": "16087320:16087320:6/8/2025 4:20:00 PM", "LFID": 16087320, "depDate": "2025-06-08T00:00:00", "flightGroupId": "16087320", "org": "KWI", "dest": "DXB", "depTime": "2025-06-08T16:20:00", "depTimeGMT": "2025-06-08T13:20:00", "arrTime": "2025-06-08T19:05:00", "operCarrier": "FZ", "operFlightNum": "060", "mrktCarrier": "FZ ", "mrktFlightNum": "060", "persons": [{"recNum": 3, "status": 0}], "legDetails": [{"PFID": 181071, "depDate": "2025-06-08T16:20:00", "legKey": "16087320:181071:6/8/2025 4:20:00 PM", "customerKey": "F45A87A4E1984DA9782BC0863189DB114A449AB4EDA7FCE7CE49C985400B8915"}], "active": true, "changeType": "TK"}, {"segKey": "16087321:16087321:6/11/2025 7:10:00 PM", "LFID": 16087321, "depDate": "2025-06-11T00:00:00", "flightGroupId": "16087321", "org": "KWI", "dest": "DXB", "depTime": "2025-06-11T19:10:00", "depTimeGMT": "2025-06-11T16:10:00", "arrTime": "2025-06-11T21:50:00", "operCarrier": "FZ", "operFlightNum": "064", "mrktCarrier": "FZ ", "mrktFlightNum": "064", "persons": [{"recNum": 4, "status": 1}], "legDetails": [{"PFID": 181072, "depDate": "2025-06-11T19:10:00", "legKey": "16087321:181072:6/11/2025 7:10:00 PM", "customerKey": "CEA0333FAD447684871A6703DAA2653A5FEBFAB6C9681506561392BD622162EC"}], "active": true, "changeType": "AC"}, {"segKey": "16087322:16087322:5/30/2025 2:25:00 PM", "LFID": 16087322, "depDate": "2025-05-30T00:00:00", "flightGroupId": "16087322", "org": "DXB", "dest": "KWI", "depTime": "2025-05-30T14:25:00", "depTimeGMT": "2025-05-30T10:25:00", "arrTime": "2025-05-30T15:20:00", "operCarrier": "FZ", "operFlightNum": "059", "mrktCarrier": "FZ ", "mrktFlightNum": "059", "persons": [{"recNum": 1, "status": 5}], "legDetails": [{"PFID": 181073, "depDate": "2025-05-30T14:25:00", "legKey": "16087322:181073:5/30/2025 2:25:00 PM", "customerKey": "2B9C43FB2437AA4695A8862A10FDFC57D7DC2492AE34409FCD750F477D8E7AB4"}], "active": true, "changeType": "TK"}, {"segKey": "16087321:16087321:6/3/2025 7:10:00 PM", "LFID": 16087321, "depDate": "2025-06-03T00:00:00", "flightGroupId": "16087321", "org": "KWI", "dest": "DXB", "depTime": "2025-06-03T19:10:00", "depTimeGMT": "2025-06-03T16:10:00", "arrTime": "2025-06-03T21:50:00", "operCarrier": "FZ", "operFlightNum": "064", "mrktCarrier": "FZ ", "mrktFlightNum": "064", "persons": [{"recNum": 2, "status": 0}], "legDetails": [{"PFID": 181072, "depDate": "2025-06-03T19:10:00", "legKey": "16087321:181072:6/3/2025 7:10:00 PM", "customerKey": "D6A9ABF7F2BCAA0DF1462390BDA9639DFF47A37818821CD412DA321CDEB73DD9"}], "active": true, "changeType": "AC"}], "persons": [{"paxID": 270600453, "fName": "MOHAMMAD", "lName": "BAHMAN", "title": "MR", "PTCID": 1, "gender": "M", "recNum": [1, 2, 3, 4], "nameChangeCount": "1"}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "L", "insuPurchasedate": "5/29/2025 5:10:41 PM", "provider": "<PERSON>", "status": 5, "fareClass": "L", "operFareClass": "L", "FBC": "LR6AE2", "fareBrand": "Lite", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "insuConfNum": "UTT9W-R9XDY-INS", "insuTransID": "UTT9W-R9XDY-INS/2cbbfc1c-e198-4c5d-854e-bbba0345c66c", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "683894ae0007770000013b1f#1#1#MOBILE#INTERNAL#CREATE", "fareTypeID": 21, "channelID": 12, "bookDate": "2025-05-29T17:10:41"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "MOBILE_APP", "cancelAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "R", "insuPurchasedate": "5/29/2025 5:10:41 PM", "provider": "<PERSON>", "status": 0, "fareClass": "R", "operFareClass": "R", "FBC": "RRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuConfNum": "UTT9W-R9XDY-INS", "insuTransID": "UTT9W-R9XDY-INS/2cbbfc1c-e198-4c5d-854e-bbba0345c66c", "toRecNum": 3, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "683894ae0007770000013b1f#1#2#MOBILE#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 12, "cancelReasonID": 0, "bookDate": "2025-05-29T17:10:41"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "MOBILE_APP", "cancelAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "H", "insuPurchasedate": "5/29/2025 5:10:41 PM", "provider": "<PERSON>", "status": 0, "fareClass": "H", "operFareClass": "H", "FBC": "HRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuConfNum": "UTT9W-R9XDY-INS", "insuTransID": "UTT9W-R9XDY-INS/2cbbfc1c-e198-4c5d-854e-bbba0345c66c", "toRecNum": 4, "fromRecNum": 2, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "683ea0550007770000039bac#270600453#2#MOBILE#VAYANT#CHANGE", "fareTypeID": 12, "channelID": 12, "cancelReasonID": 0, "bookDate": "2025-06-03T07:12:58"}]}, {"recNum": 4, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "L", "insuPurchasedate": "6/6/2025 12:43:32 PM", "provider": "<PERSON>", "status": 1, "fareClass": "L", "operFareClass": "L", "FBC": "LRB7AE2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "2BVED-UB7SJ-INS/a5ea3dbd-bb6d-419f-9e38-be26fe64f66f", "fromRecNum": 3, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6842e2410007770000025c76#270600453#2#MOBILE#VAYANT#CHANGE", "fareTypeID": 12, "channelID": 12, "bookDate": "2025-06-06T12:43:32"}]}], "payments": [{"paymentID": *********, "paxID": 271396792, "method": "IPAY", "status": "1", "paidDate": "2025-06-06T12:43:35", "cardNum": "************4677", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 183.19, "baseCurr": "AED", "baseAmt": 183.19, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "ABDULLAH Bahman", "authCode": "686579", "reference": "23489431", "externalReference": "23489431", "tranId": "21867206", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21867206}, {"paymentID": *********, "paxID": 270600937, "method": "IPAY", "status": "1", "paidDate": "2025-05-29T17:14:28", "cardNum": "************4677", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 1702.28, "baseCurr": "AED", "baseAmt": 1702.28, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "ABDULLAH Bahman", "authCode": "686533", "reference": "23327749", "externalReference": "23327749", "tranId": "21712395", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21712395}, {"paymentID": 210734524, "paxID": 270600952, "method": "VISA", "status": "2", "paidDate": "2025-05-29T17:13:54", "cardNum": "************4066", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 1702.28, "baseCurr": "AED", "baseAmt": 1702.28, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "MOHAMMAD BAHMAN", "reference": "23327685", "externalReference": "23327685", "tranId": "21712395", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21712395}, {"paymentID": *********, "paxID": 271045085, "method": "IPAY", "status": "1", "paidDate": "2025-06-03T07:13:01", "cardNum": "************4677", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 751.9, "baseCurr": "AED", "baseAmt": 751.9, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "ABDULLAH Bahman", "authCode": "686558", "reference": "23419797", "externalReference": "23419797", "tranId": "21800282", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21800282}], "contacts": [{"key": 330672716, "paxID": 270600453, "contactField": "<EMAIL>", "contactType": "Email", "PreferenceOrder": "1", "countryCode": "", "areaCode": "", "phoneNumber": ""}, {"key": 330672715, "paxID": 270600453, "contactField": "971506245484", "contactType": "MobilePhone", "preferred": true, "PreferenceOrder": "1", "countryCode": "971", "areaCode": "", "phoneNumber": "506245484"}, {"key": 330672714, "paxID": 270600453, "contactField": "971506245484", "contactType": "WhatsApp", "PreferenceId": 13217158, "PreferenceOrder": "1", "countryCode": "971", "areaCode": "", "phoneNumber": "506245484"}], "OAFlights": null, "physicalFlights": [{"key": "16087322:181073:2025-05-30T02:25:00 PM", "LFID": 16087322, "PFID": 181073, "org": "DXB", "dest": "KWI", "depDate": "2025-05-30T14:25:00", "depTime": "2025-05-30T14:25:00", "arrTime": "2025-05-30T15:20:00", "carrier": "FZ", "flightNum": "059", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 1", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "059", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "KWI", "operatingCarrier": "FZ", "flightDuration": 6900, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Kuwait International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "2/7/2025 7:23:19 AM"}, {"key": "16087321:181072:2025-06-03T07:10:00 PM", "LFID": 16087321, "PFID": 181072, "org": "KWI", "dest": "DXB", "depDate": "2025-06-03T19:10:00", "depTime": "2025-06-03T19:10:00", "arrTime": "2025-06-03T21:50:00", "carrier": "FZ", "flightNum": "064", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73V", "mrktCarrier": "FZ", "mrktFlightNum": "064", "flightStatus": "CLOSED", "originMetroGroup": "KWI", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 6000, "reaccomChangeAlert": false, "originName": "Kuwait International Airport", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "AC", "flightChangeTime": "3/5/2025 11:39:15 AM"}, {"key": "16087320:181071:2025-06-08T04:20:00 PM", "LFID": 16087320, "PFID": 181071, "org": "KWI", "dest": "DXB", "depDate": "2025-06-08T16:20:00", "depTime": "2025-06-08T16:20:00", "arrTime": "2025-06-08T19:05:00", "carrier": "FZ", "flightNum": "060", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "060", "flightStatus": "CLOSED", "originMetroGroup": "KWI", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 6300, "reaccomChangeAlert": false, "originName": "Kuwait International Airport", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "2/7/2025 7:24:40 AM"}, {"key": "16087321:181072:2025-06-11T07:10:00 PM", "LFID": 16087321, "PFID": 181072, "org": "KWI", "dest": "DXB", "depDate": "2025-06-11T19:10:00", "depTime": "2025-06-11T19:10:00", "arrTime": "2025-06-11T21:50:00", "carrier": "FZ", "flightNum": "064", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73V", "mrktCarrier": "FZ", "mrktFlightNum": "064", "flightStatus": "OPEN", "originMetroGroup": "KWI", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 6000, "reaccomChangeAlert": false, "originName": "Kuwait International Airport", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "AC", "flightChangeTime": "3/10/2025 6:36:26 AM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1378737326, "codeType": "INSU", "amt": -17.85, "curr": "AED", "originalAmt": -17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-06T12:43:32", "desc": "INSU", "comment": "Reverse due to <PERSON>", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1367946583, "paymentMap": [{"key": "1378737326:*********", "paymentID": *********, "amt": -17.85, "approveCode": 0}], "isSSR": true, "ChargeBookDate": "2025-06-06T12:43:32"}, {"chargeID": 1367946583, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T17:10:41", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367946583:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}"}, {"chargeID": 1367946570, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1367946567, "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-29T17:10:41", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367946570:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1367946569, "codeType": "TAX", "taxID": 11586, "taxCode": "N4", "taxChargeID": 1367946567, "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-29T17:10:41", "desc": "Arrival and Departure Passenger Service Charge", "comment": "Arrival and Departure Passenger Service Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367946569:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": 1367946571, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1367946567, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-29T17:10:41", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367946571:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1367946573, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1367946567, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-29T17:10:41", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367946573:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1367946572, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1367946567, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-29T17:10:41", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367946572:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1367946568, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1367946567, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-29T17:10:41", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367946568:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1367946567, "codeType": "AIR", "amt": 220, "curr": "AED", "originalAmt": 220, "originalCurr": "AED", "status": 1, "billDate": "2025-05-29T17:10:41", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367946567:*********", "paymentID": *********, "amt": 220, "approveCode": 0}]}, {"chargeID": 1367953769, "codeType": "PMNT", "amt": 49.58, "curr": "AED", "originalAmt": 49.58, "originalCurr": "AED", "status": 1, "billDate": "2025-05-29T17:14:30", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367953769:*********", "paymentID": *********, "amt": 49.58, "approveCode": 0}]}, {"chargeID": 1367946584, "codeType": "XLGR", "amt": 166, "curr": "AED", "originalAmt": 166, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-29T17:10:41", "desc": "XLGR", "comment": "FLXID:XLGR_73B_ZONE2_WIN_AIS::181073", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367946584:*********", "paymentID": *********, "amt": 166, "approveCode": 0}], "PFID": "181073"}]}, {"recNum": 2, "charges": [{"chargeID": 1367946586, "codeType": "INSU", "amt": 17.85, "curr": "AED", "originalAmt": 17.85, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T17:10:41", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367946586:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"9.72\",\r\n  \"Tax\": \"0.46\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-05-29T17:10:41"}, {"chargeID": 1374023665, "codeType": "TAX", "taxID": 11586, "taxCode": "N4", "taxChargeID": 1367946574, "amt": -40, "curr": "AED", "originalAmt": -40, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-03T07:12:58", "desc": "Arrival and Departure Passenger Service Charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1367946577, "paymentMap": [{"key": "1374023665:*********", "paymentID": *********, "amt": -40, "approveCode": 0}]}, {"chargeID": 1374023670, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1367946574, "amt": -80, "curr": "AED", "originalAmt": -80, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-03T07:12:58", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1367946578, "paymentMap": [{"key": "1374023670:*********", "paymentID": *********, "amt": -80, "approveCode": 0}]}, {"chargeID": 1374023669, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1367946574, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-03T07:12:58", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1367946576, "paymentMap": [{"key": "1374023669:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1374023664, "codeType": "TAX", "taxID": 10406, "taxCode": "GZ", "taxChargeID": 1367946574, "amt": -20, "curr": "AED", "originalAmt": -20, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-03T07:12:58", "desc": "Airport Service Charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1367946579, "paymentMap": [{"key": "1374023664:*********", "paymentID": *********, "amt": -20, "approveCode": 0}]}, {"chargeID": 1374023668, "codeType": "TAX", "taxID": 5784, "taxCode": "YX", "taxChargeID": 1367946574, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-03T07:12:58", "desc": "Passenger Service Charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1367946581, "paymentMap": [{"key": "1374023668:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1374023667, "codeType": "TAX", "taxID": 13031, "taxCode": "KW", "taxChargeID": 1367946574, "amt": -30, "curr": "AED", "originalAmt": -30, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-03T07:12:58", "desc": "Airport and Passenger Service Charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1367946580, "paymentMap": [{"key": "1374023667:*********", "paymentID": *********, "amt": -30, "approveCode": 0}]}, {"chargeID": 1367946577, "codeType": "TAX", "taxID": 11586, "taxCode": "N4", "taxChargeID": 1367946574, "amt": 40, "curr": "AED", "originalAmt": 40, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T17:10:41", "desc": "Arrival and Departure Passenger Service Charge", "comment": "Arrival and Departure Passenger Service Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367946577:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1367946579, "codeType": "TAX", "taxID": 10406, "taxCode": "GZ", "taxChargeID": 1367946574, "amt": 20, "curr": "AED", "originalAmt": 20, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T17:10:41", "desc": "Airport Service Charge", "comment": "Airport Service Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367946579:*********", "paymentID": *********, "amt": 20, "approveCode": 0}]}, {"chargeID": 1367946578, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1367946574, "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T17:10:41", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367946578:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1367946580, "codeType": "TAX", "taxID": 13031, "taxCode": "KW", "taxChargeID": 1367946574, "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T17:10:41", "desc": "Airport and Passenger Service Charge", "comment": "Airport and Passenger Service Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367946580:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": 1367946576, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1367946574, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T17:10:41", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367946576:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1367946581, "codeType": "TAX", "taxID": 5784, "taxCode": "YX", "taxChargeID": 1367946574, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T17:10:41", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367946581:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1374023666, "codeType": "AIR", "amt": -640, "curr": "AED", "originalAmt": -640, "originalCurr": "AED", "status": 0, "billDate": "2025-06-03T07:12:58", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1367946574, "paymentMap": [{"key": "1374023666:*********", "paymentID": *********, "amt": -640, "approveCode": 0}]}, {"chargeID": 1367946574, "codeType": "AIR", "amt": 640, "curr": "AED", "originalAmt": 640, "originalCurr": "AED", "status": 0, "billDate": "2025-05-29T17:10:41", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367946574:*********", "paymentID": *********, "amt": 640, "approveCode": 0}]}, {"chargeID": 1367946587, "codeType": "XLGR", "amt": 166, "curr": "AED", "originalAmt": 166, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T17:10:41", "desc": "XLGR", "comment": "FLXID:XLGR_73V_ZONE2_WIN_AIS::181072", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1367946587:*********", "paymentID": *********, "amt": 166, "approveCode": 0}], "PFID": "181072"}, {"chargeID": 1374023671, "codeType": "PNLT", "amt": 720, "curr": "AED", "originalAmt": 720, "originalCurr": "AED", "status": 1, "billDate": "2025-06-03T07:12:58", "desc": "Penalty AddedDueToModify FZ  064 KWI  - DXB  03-Jun-2025", "comment": "Penalty Added Due To Modify", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1374023671:*********", "paymentID": *********, "amt": 720, "approveCode": 0}]}, {"chargeID": 1367946575, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1367946574, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T17:10:41", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1367946588, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-29T17:10:41", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181072"}]}, {"recNum": 4, "charges": [{"chargeID": 1378737325, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 1, "exchRate": 1, "billDate": "2025-06-06T12:43:32", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1378737325:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"SegPaxCount\":\"1\"}", "ChargeBookDate": "2025-06-06T12:43:32"}, {"chargeID": 1378737202, "codeType": "TAX", "taxID": 5784, "taxCode": "YX", "taxChargeID": 1378737196, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-06T12:43:32", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1378737202:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1378737200, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1378737196, "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-06T12:43:32", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1378737200:*********", "paymentID": *********, "amt": 62.15, "approveCode": 0}, {"key": "1378737200:*********", "paymentID": *********, "amt": 17.85, "approveCode": 0}]}, {"chargeID": 1378737198, "codeType": "TAX", "taxID": 11586, "taxCode": "N4", "taxChargeID": 1378737196, "amt": 40, "curr": "AED", "originalAmt": 40, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-06T12:43:32", "desc": "Arrival and Departure Passenger Service Charge", "comment": "Arrival and Departure Passenger Service Charge", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1378737198:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1378737201, "codeType": "TAX", "taxID": 13031, "taxCode": "KW", "taxChargeID": 1378737196, "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-06T12:43:32", "desc": "Airport and Passenger Service Charge", "comment": "Airport and Passenger Service Charge", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1378737201:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": 1378737197, "codeType": "TAX", "taxID": 10406, "taxCode": "GZ", "taxChargeID": 1378737196, "amt": 20, "curr": "AED", "originalAmt": 20, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-06T12:43:32", "desc": "Airport Service Charge", "comment": "Airport Service Charge", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1378737197:*********", "paymentID": *********, "amt": 20, "approveCode": 0}]}, {"chargeID": 1378737199, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1378737196, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-06T12:43:32", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1378737199:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1378737196, "codeType": "AIR", "amt": 660, "curr": "AED", "originalAmt": 660, "originalCurr": "AED", "status": 1, "billDate": "2025-06-06T12:43:32", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1378737196:*********", "paymentID": *********, "amt": 142.15, "approveCode": 0}, {"key": "1378737196:*********", "paymentID": *********, "amt": 517.85, "approveCode": 0}]}, {"chargeID": 1378738067, "codeType": "PMNT", "amt": 5.34, "curr": "AED", "originalAmt": 5.34, "originalCurr": "AED", "status": 1, "billDate": "2025-06-06T12:43:38", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1378738067:*********", "paymentID": *********, "amt": 5.34, "approveCode": 0}]}, {"chargeID": 1378737203, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1378737196, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-06T12:43:32", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1378737209, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-06-06T12:43:32", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181072"}]}, {"recNum": 3, "charges": [{"chargeID": 1374023675, "codeType": "TAX", "taxID": 11586, "taxCode": "N4", "taxChargeID": 1374023673, "amt": 40, "curr": "AED", "originalAmt": 40, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-03T07:12:58", "desc": "Arrival and Departure Passenger Service Charge", "comment": "Arrival and Departure Passenger Service Charge", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1374023675:*********", "paymentID": *********, "amt": 40, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-06-03T07:12:58"}, {"chargeID": 1374023676, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1374023673, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-03T07:12:58", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1374023676:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1374023677, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1374023673, "amt": 80, "curr": "AED", "originalAmt": 80, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-03T07:12:58", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1374023677:*********", "paymentID": *********, "amt": 80, "approveCode": 0}]}, {"chargeID": 1374023679, "codeType": "TAX", "taxID": 5784, "taxCode": "YX", "taxChargeID": 1374023673, "amt": 10, "curr": "AED", "originalAmt": 10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-03T07:12:58", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1374023679:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1374023678, "codeType": "TAX", "taxID": 13031, "taxCode": "KW", "taxChargeID": 1374023673, "amt": 30, "curr": "AED", "originalAmt": 30, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-03T07:12:58", "desc": "Airport and Passenger Service Charge", "comment": "Airport and Passenger Service Charge", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1374023678:*********", "paymentID": *********, "amt": 30, "approveCode": 0}]}, {"chargeID": 1374023674, "codeType": "TAX", "taxID": 10406, "taxCode": "GZ", "taxChargeID": 1374023673, "amt": 20, "curr": "AED", "originalAmt": 20, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-03T07:12:58", "desc": "Airport Service Charge", "comment": "Airport Service Charge", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1374023674:*********", "paymentID": *********, "amt": 20, "approveCode": 0}]}, {"chargeID": 1378737191, "codeType": "TAX", "taxID": 5784, "taxCode": "YX", "taxChargeID": 1374023673, "amt": -10, "curr": "AED", "originalAmt": -10, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-06T12:43:32", "desc": "Passenger Service Charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1374023679, "paymentMap": [{"key": "1378737191:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1378737187, "codeType": "TAX", "taxID": 11586, "taxCode": "N4", "taxChargeID": 1374023673, "amt": -40, "curr": "AED", "originalAmt": -40, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-06T12:43:32", "desc": "Arrival and Departure Passenger Service Charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1374023675, "paymentMap": [{"key": "1378737187:*********", "paymentID": *********, "amt": -40, "approveCode": 0}]}, {"chargeID": 1378737188, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1374023673, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-06T12:43:32", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1374023676, "paymentMap": [{"key": "1378737188:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1378737189, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1374023673, "amt": -80, "curr": "AED", "originalAmt": -80, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-06T12:43:32", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1374023677, "paymentMap": [{"key": "1378737189:*********", "paymentID": *********, "amt": -80, "approveCode": 0}]}, {"chargeID": 1378737192, "codeType": "TAX", "taxID": 13031, "taxCode": "KW", "taxChargeID": 1374023673, "amt": -30, "curr": "AED", "originalAmt": -30, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-06T12:43:32", "desc": "Airport and Passenger Service Charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1374023678, "paymentMap": [{"key": "1378737192:*********", "paymentID": *********, "amt": -30, "approveCode": 0}]}, {"chargeID": 1378737193, "codeType": "TAX", "taxID": 10406, "taxCode": "GZ", "taxChargeID": 1374023673, "amt": -20, "curr": "AED", "originalAmt": -20, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-06T12:43:32", "desc": "Airport Service Charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1374023674, "paymentMap": [{"key": "1378737193:*********", "paymentID": *********, "amt": -20, "approveCode": 0}]}, {"chargeID": 1374023673, "codeType": "AIR", "amt": 650, "curr": "AED", "originalAmt": 650, "originalCurr": "AED", "status": 0, "billDate": "2025-06-03T07:12:58", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1374023673:*********", "paymentID": *********, "amt": 650, "approveCode": 0}]}, {"chargeID": 1378737190, "codeType": "AIR", "amt": -650, "curr": "AED", "originalAmt": -650, "originalCurr": "AED", "status": 0, "billDate": "2025-06-06T12:43:32", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1374023673, "paymentMap": [{"key": "1378737190:*********", "paymentID": *********, "amt": -650, "approveCode": 0}]}, {"chargeID": 1374024655, "codeType": "PMNT", "amt": 21.9, "curr": "AED", "originalAmt": 21.9, "originalCurr": "AED", "status": 0, "billDate": "2025-06-03T07:13:04", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1374024655:*********", "paymentID": *********, "amt": 21.9, "approveCode": 0}]}, {"chargeID": 1378737194, "codeType": "PNLT", "amt": 150, "curr": "AED", "originalAmt": 150, "originalCurr": "AED", "status": 1, "billDate": "2025-06-06T12:43:32", "desc": "Penalty AddedDueToModify FZ  060 KWI  - DXB  08-Jun-2025", "comment": "Penalty Added Due To Modify", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1378737194:*********", "paymentID": *********, "amt": 150, "approveCode": 0}]}, {"chargeID": 1374023680, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1374023673, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-03T07:12:58", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1374023687, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-06-03T07:12:58", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181071"}]}], "parentPNRs": [], "childPNRs": []}