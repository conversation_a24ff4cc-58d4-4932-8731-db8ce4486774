{"pnr": "44I68W", "policyId": "7EVRP-BESEZ-INS", "timestamp": "2025-06-09T09:36:06.773Z", "requestDetails": {"url": "https://api.xcover.com/x/partners/ZDTIY/bookings/7EVRP-BESEZ-INS", "method": "POST", "headers": {"Content-Type": "application/json"}, "payload": {"quotes": [{"id": "0899ca05-1b23-45ce-95c2-61ba84fa9b9f", "insured": [{"first_name": "HANAN", "last_name": "FRAIWAT"}]}], "policyholder": {"first_name": "HANAN", "last_name": "FRAIWAT", "email": "<EMAIL>", "country": "AE"}}}, "curlCommand": "curl --location 'https://api.xcover.com/x/partners/ZDTIY/bookings/7EVRP-BESEZ-INS' \\\n--header 'Content-Type: application/json' \\\n--data-raw '{\n  \"quotes\": [\n    {\n      \"id\": \"0899ca05-1b23-45ce-95c2-61ba84fa9b9f\",\n      \"insured\": [\n        {\n          \"first_name\": \"HANAN\",\n          \"last_name\": \"FRAIWAT\"\n        }\n      ]\n    }\n  ],\n  \"policyholder\": {\n    \"first_name\": \"HANAN\",\n    \"last_name\": \"FRAIWAT\",\n    \"email\": \"<EMAIL>\",\n    \"country\": \"AE\"\n  }\n}'", "instructions": ["1. Verify the email address in the policyholder section is correct", "2. Verify passenger names are correct", "3. Confirm the country code is appropriate", "4. Execute the curl command manually", "5. Save the response for record keeping"], "notes": {"extractedPassengers": 1, "extractedEmail": "<EMAIL>", "primaryPassengerId": 270151141, "firstSegmentOrigin": "DXB", "detectedCountry": "AE", "quoteId": "0899ca05-1b23-45ce-95c2-61ba84fa9b9f", "quoteIdSource": "insuTransID", "hasContactInfo": true, "emailFromPrimaryPax": true}}