'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Copy, Download, Eye, EyeOff } from 'lucide-react';
import { PNRRecord, InsuranceRecord } from '@/types';

interface ResultsTableProps {
  results: PNRRecord[];
}

export function ResultsTable({ results }: ResultsTableProps) {

  const [showAllRecords, setShowAllRecords] = useState<{ [key: string]: boolean }>({});

  const completedResults = results.filter(r => r.status === 'completed' && r.result && !r.result.error);
  const errorResults = results.filter(r => r.status === 'error' || (r.result && r.result.error));

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      // You could add a toast notification here
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  const exportResults = () => {
    const allInsuranceRecords: Record<string, unknown>[] = [];
    const allSqlQueries: string[] = [];

    completedResults.forEach(result => {
      if (result.result) {
        // Add insurance records
        result.result.insuranceRecords.forEach(record => {
          allInsuranceRecords.push({
            PNR: result.pnr,
            PolicyID: result.result!.policyId,
            ...record
          });
        });

        // Add SQL queries
        result.result.sqlQueries.forEach(query => {
          allSqlQueries.push(`-- PNR: ${result.pnr}, Record: ${query.recordNumber}, Passenger: ${query.passenger}`);
          allSqlQueries.push(query.query);
          allSqlQueries.push('');
        });
      }
    });

    // Create CSV content
    const csvContent = [
      Object.keys(allInsuranceRecords[0] || {}).join(','),
      ...allInsuranceRecords.map(record =>
        Object.values(record).map(value =>
          typeof value === 'string' && value.includes(',') ? `"${value}"` : value
        ).join(',')
      )
    ].join('\n');

    // Create SQL content
    const sqlContent = allSqlQueries.join('\n');

    // Download CSV
    const csvBlob = new Blob([csvContent], { type: 'text/csv' });
    const csvUrl = URL.createObjectURL(csvBlob);
    const csvLink = document.createElement('a');
    csvLink.href = csvUrl;
    csvLink.download = `insurance-records-${new Date().toISOString().split('T')[0]}.csv`;
    csvLink.click();

    // Download SQL
    const sqlBlob = new Blob([sqlContent], { type: 'text/sql' });
    const sqlUrl = URL.createObjectURL(sqlBlob);
    const sqlLink = document.createElement('a');
    sqlLink.href = sqlUrl;
    sqlLink.download = `update-queries-${new Date().toISOString().split('T')[0]}.sql`;
    sqlLink.click();
  };

  const toggleShowAllRecords = (pnr: string) => {
    setShowAllRecords(prev => ({
      ...prev,
      [pnr]: !prev[pnr]
    }));
  };

  const getStatusBadge = (record: InsuranceRecord) => {
    if (record.hasError) {
      return <Badge variant="destructive">Policy Error</Badge>;
    }
    if (!record.hasConfirmation) {
      return <Badge variant="destructive">Missing Confirmation</Badge>;
    }
    if (!record.withinPolicyPeriod) {
      return <Badge variant="secondary">Outside Policy Period</Badge>;
    }
    return <Badge variant="default">OK</Badge>;
  };

  if (completedResults.length === 0 && errorResults.length === 0) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Processing Results</CardTitle>
          <CardDescription>Results will appear here as PNRs are processed</CardDescription>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertDescription>
              No completed results yet. Start processing PNRs to see the insurance data.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Summary Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Processing Results Summary</span>
            <Button onClick={exportResults} size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export All
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold">{completedResults.length}</div>
              <div className="text-sm text-gray-600">Successful PNRs</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{errorResults.length}</div>
              <div className="text-sm text-gray-600">Failed PNRs</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {completedResults.reduce((sum, r) => sum + (r.result?.summary.missingConfirmation || 0), 0)}
              </div>
              <div className="text-sm text-gray-600">Missing Confirmations</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {completedResults.reduce((sum, r) => sum + (r.result?.insuranceRecords.filter(rec => rec.hasError).length || 0), 0)}
              </div>
              <div className="text-sm text-gray-600">Policy Errors</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">
                {completedResults.reduce((sum, r) => sum + (r.result?.summary.totalRecords || 0), 0)}
              </div>
              <div className="text-sm text-gray-600">Total Records</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">
                {completedResults.reduce((sum, r) => sum + (r.result?.sqlQueries.length || 0), 0)}
              </div>
              <div className="text-sm text-gray-600">SQL Queries</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Individual PNR Results */}
      {completedResults.map((result) => {
        if (!result.result) return null;

        const problemRecords = result.result.insuranceRecords.filter(r => !r.hasConfirmation || r.hasError);
        const showAll = showAllRecords[result.pnr];
        const recordsToShow = showAll ? result.result.insuranceRecords : problemRecords;

        return (
          <Card key={result.pnr}>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>PNR: {result.pnr}</span>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => toggleShowAllRecords(result.pnr)}
                  >
                    {showAll ? <EyeOff className="h-4 w-4 mr-2" /> : <Eye className="h-4 w-4 mr-2" />}
                    {showAll ? 'Show Problems Only' : 'Show All Records'}
                  </Button>
                </div>
              </CardTitle>
              <CardDescription>
                Policy: {result.result.policyId} |
                Total: {result.result.summary.totalRecords} |
                Missing: {result.result.summary.missingConfirmation} |
                Errors: {result.result.insuranceRecords.filter(r => r.hasError).length}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {/* Insurance Records Table */}
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Record #</TableHead>
                      <TableHead>Passenger</TableHead>
                      <TableHead>Flight</TableHead>
                      <TableHead>Departure</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Confirmation</TableHead>
                      <TableHead>Policy ID</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {recordsToShow.map((record) => (
                      <TableRow key={`${result.pnr}-${record.recordNumber}`} className={record.hasError ? 'bg-red-50' : ''}>
                        <TableCell>{record.recordNumber}</TableCell>
                        <TableCell>
                          <div>
                            {record.passengerName}
                            {record.hasError && (
                              <div className="text-xs text-red-600 mt-1">
                                {record.errorMessage}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          {record.segmentInfo ? record.segmentInfo.flightNumber : 'N/A'}
                        </TableCell>
                        <TableCell>
                          {record.departureDate ? new Date(record.departureDate).toLocaleDateString() : 'N/A'}
                        </TableCell>
                        <TableCell>{getStatusBadge(record)}</TableCell>
                        <TableCell>
                          {record.hasError ? 'Error' : (record.hasConfirmation ? record.insuConfNum || 'N/A' : 'Missing')}
                        </TableCell>
                        <TableCell>
                          <span className={record.hasError ? 'text-red-600 font-medium' : ''}>
                            {record.policyId || result.result?.policyId || 'N/A'}
                          </span>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* SQL Queries */}
              {result.result.sqlQueries.length > 0 && (
                <div className="mt-6 space-y-3">
                  <h4 className="font-semibold">SQL Update Queries</h4>
                  {result.result.sqlQueries.map((query, index) => (
                    <div key={index} className="bg-gray-900 text-gray-100 p-3 rounded-lg">
                      <div className="flex justify-between items-start mb-2">
                        <span className="text-sm text-gray-400">
                          Record {query.recordNumber} - {query.passenger} {query.policyId && `(Policy: ${query.policyId})`}
                        </span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyToClipboard(query.query)}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                      <code className="text-sm font-mono">{query.query}</code>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        );
      })}

      {/* Error Results */}
      {errorResults.map((result) => (
        <Card key={result.pnr} className="border-red-200">
          <CardHeader>
            <CardTitle className="flex items-center justify-between text-red-600">
              <span>PNR: {result.pnr} (Error)</span>
              <Badge variant="destructive">Failed</Badge>
            </CardTitle>
            <CardDescription className="text-red-600">
              {result.error || (result.result && result.result.error) || 'Processing failed'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Alert variant="destructive">
              <AlertDescription>
                This PNR could not be processed successfully. Please check the error message above and try again.
                {result.result && result.result.errorType === 'COVER_GENIUS_API_ERROR' && (
                  <span className="block mt-2">
                    <strong>Note:</strong> This appears to be a Cover Genius API error. The policy ID may be invalid or the API may be temporarily unavailable.
                  </span>
                )}
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
